@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";



@import "lib/simple-notify.min.css";
@import "_partial/layer.css";
@import "_partial/md_content.css";
@import "_partial/md_table.css";
@import "_partial/bsa_custom_sticky_bar.css";
@import "_partial/_components.css";
@import "_partial/_convert.css";
@import "_partial/_share.css";
@import "_partial/modal.css";

/* ========================================
   TableConvert Modern UI Enhancements
   ======================================== */

/* Modern gradient backgrounds */
.gradient-data-input {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    position: relative;
    overflow: hidden;
}

.gradient-data-input::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
    opacity: 0.3;
}

/* Modern button styles */
.btn-modern {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-modern:hover::before {
    left: 100%;
}

/* Buy me a coffee button with light sweep effect */
.btn-coffee {
    background: #6b7def;
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
}

.btn-coffee::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-coffee:hover::before {
    left: 100%;
}

/* Tool button enhancements */
.tool-button-modern {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(59, 130, 246, 0.2);
    transition: all 0.2s ease;
}

.tool-button-modern:hover {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.4);
    transform: translateY(-1px);
}

.dark .tool-button-modern {
    background: rgba(30, 41, 59, 0.9);
    border-color: rgba(71, 85, 105, 0.3);
}

.dark .tool-button-modern:hover {
    background: rgba(59, 130, 246, 0.2);
}

/* Enhanced section styling */
._dataSource, ._editor, .generator-section {
    @apply rounded-2xl;
}

/* Enhanced dropdown menus - moved to unified section below */

/* Enhanced editor tools */
._editor ._button {
    @apply tool-button-modern rounded-xl shadow-none;
    border: 1px solid rgba(59, 130, 246, 0.2) !important;
}

._editor ._button:hover {
    @apply bg-blue-50 dark:bg-slate-800;
    transform: translateY(-1px);
}

/* Enhanced Modern Format Tabs */
.format-tabs {
    background: rgba(248, 250, 252, 0.9);
    border: none;
    border-radius: 0;
    padding: 8px 0;
    padding-left: 24px;
    padding-right: 24px;
}

.dark .format-tabs {
    background: rgba(15, 23, 42, 0.9);
}

.format-tab {
    @apply px-5 py-1.5 rounded-full text-sm font-medium transition-all duration-200 relative;
    border: 1px solid transparent;
    white-space: nowrap;
}

.format-tab.active {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.format-tab:not(.active) {
    @apply text-gray-600 dark:text-gray-300;
}

.format-tab:not(.active):hover {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    transform: translateY(-1px);
    color: #3b82f6;
}

.dark .format-tab:not(.active):hover {
    background: rgba(59, 130, 246, 0.2);
    color: #60a5fa;
}

/* Enhanced page background */
body {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.dark body {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* Enhanced section spacing */
.max-container > section + section {
    margin-top: 2rem;
}

/* Enhanced button hover effects */
.btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Enhanced Modern Editor Tools */
.editor-tools {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 16px;
}

.dark .editor-tools {
    background: rgba(30, 41, 59, 0.9);
    border: 1px solid rgba(71, 85, 105, 0.4);
}

/* Enhanced Replace All Button */
.replace-all-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 10px 24px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
    position: relative;
    overflow: hidden;
    height: 38px;
}

.replace-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.replace-all-btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

/* Enhanced input fields */
.find-replace-input {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: 10px;
    padding: 10px 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 14px;
}

.dark .find-replace-input {
    background: rgba(51, 65, 85, 0.9);
    border: 1px solid rgba(71, 85, 105, 0.6);
    color: #e2e8f0;
}

.find-replace-input:focus {
    outline: none;
    border: 1px solid #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: rgba(255, 255, 255, 1);
}

.dark .find-replace-input:focus {
    background: rgba(51, 65, 85, 1);
    border: 1px solid #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

/* Enhanced Output Panel */
.output-panel {
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(71, 85, 105, 0.3);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 13px;
    line-height: 1.6;
    color: #e2e8f0;
    position: relative;
    overflow: hidden;
}

.output-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
}

.output-panel pre {
    margin: 0;
    padding: 0;
    background: transparent;
    border: none;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    color: inherit;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Enhanced code syntax highlighting */
.output-panel .string {
    color: #10b981;
}

.output-panel .number {
    color: #f59e0b;
}

.output-panel .boolean {
    color: #8b5cf6;
}

.output-panel .null {
    color: #ef4444;
}

.output-panel .key {
    color: #3b82f6;
}

/* Responsive optimizations */
@media (max-width: 768px) {
    .editor-tools {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        padding: 16px;
    }

    .format-tabs {
        padding: 6px;
        border-radius: 12px;
    }

    .format-tab {
        padding: 8px 12px;
        font-size: 12px;
    }

    .output-panel {
        padding: 16px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .editor-tools {
        grid-template-columns: repeat(2, 1fr);
    }

    .format-tabs {
        flex-wrap: wrap;
        gap: 4px;
    }

    .format-tab {
        flex: 1;
        min-width: 60px;
        text-align: center;
    }
}

/* Enhanced animations */
@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
    }
}

.pulse-on-click {
    animation: pulse-glow 0.6s ease-out;
}

.format-tab:active {
    transform: translateY(0) scale(0.98);
}

.replace-all-btn:active {
    transform: translateY(0) scale(0.98);
}

/* Loading states */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Success feedback */
.success-feedback {
    animation: success-pulse 0.6s ease-out;
}

@keyframes success-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(34, 197, 94, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
}

.format-tab:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.find-replace-input:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Generator Header Button Styles */
.generator-header-btn {
    display: inline-flex;
    align-items: center;
    border: 1px solid #3b82f6;
    border-radius: 9999px;
    padding: 0 20px;
    font-size: 14px;
    line-height: 32px;
    user-select: none;
    color: #3b82f6;
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    cursor: pointer;
}

.generator-header-btn:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.generator-header-btn:focus {
    outline: none;
}

.dark .generator-header-btn {
    background: rgba(30, 41, 59, 0.9);
    border-color: #60a5fa;
    color: #60a5fa;
}

.dark .generator-header-btn:hover {
    background: #60a5fa;
    color: #1e293b;
}

/* Enhanced scroll behavior */
.format-tabs {
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.format-tabs::-webkit-scrollbar {
    display: none;
}

/* Improved accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .format-tab {
        border: 2px solid currentColor;
    }

    .format-tab.active {
        background: currentColor;
        color: white;
    }
}

html {
    /* https://developer.mozilla.org/en-US/docs/Web/CSS/-webkit-tap-highlight-color */
    -webkit-tap-highlight-color: transparent;
    scroll-behavior: smooth;
    scroll-padding-top: 50px;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
}

body {
    font-family: 'Inter', sans-serif;
}

.max-container {
    max-width: 1580px;
    @apply mx-auto p-3 md:p-6;
}

.fullscreen {
    z-index: 999999;
    width: 100% !important;
    height: 100% !important;
    position: fixed;
    top: 0;
    left: 0;
    padding: 0.75rem !important;
    margin: 0 !important;
    background: white;
    border-radius: 0;
    @apply openAnimation;
}
.fullscreen .editor-switch {
    display: none;
}
.fullscreen #grid {
   margin-bottom: 40px;
}


.file.draggedover {
    @apply border-blue-400 bg-blue-50;
    border-width: 3px;
    border-style: dashed;
}

.dark .file.draggedover {
    @apply border-blue-400 bg-blue-900/20;
}


input:checked + .icon-tick {
    display: block;
}

/* 搜索结果面板动画 */
.search-content {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

#search:focus-within .search-content {
    opacity: 1;
    transform: translateY(0) scale(1);
    visibility: visible;
}



/* 下拉面板动画 */
.dropdown-panel {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-panel.show {
    opacity: 1;
    transform: translateY(0) scale(1);
    visibility: visible !important;
}

/* 下拉菜单项选中状态 */
.dropdown-menu a.selected {
    @apply bg-slate-300 dark:bg-slate-700 dark:text-white;
}

.search-content .items > a {
    @apply block p-2 cursor-pointer transition duration-100 ease-in-out;
    @apply hover:bg-slate-300 focus:outline-none dark:text-slate-400 dark:hover:text-white dark:hover:bg-slate-700;
}

.search-content .items > a.selected {
    @apply bg-slate-300 dark:bg-slate-700 dark:text-white;
}

/* 搜索关键词高亮 */
.search-content .items > a mark {
    @apply bg-yellow-200 dark:bg-yellow-600 text-yellow-900 dark:text-yellow-100 rounded px-1;
}

.icon-rounded-full {
    @apply w-7 h-7 inline-flex items-center justify-center rounded-full text-white flex-shrink-0 opacity-90;
}

.output .items > a {
   @apply rounded-full border-2 border-transparent font-bold text-sm text-slate-600  text-center tracking-wide leading-5 py-1 px-5 opacity-90;
   @apply hover:bg-white hover:shadow hover:border-blue-500;
   @apply dark:hover:bg-slate-800 dark:hover:border-slate-600 dark:text-slate-300 dark:hover:shadow-none;
   @apply transition duration-100 ease-in-out;
}

.output .items > a.active {
   @apply bg-blue-600 text-white shadow-lg opacity-100;
   @apply dark:hover:bg-blue-600 dark:hover:border-blue-500;
}


.group:hover .group-hover\:scale-100 {
    transform: scale(1);
}

.table-chooser-wrapper table {
    border-collapse: separate;
    border-spacing: 1px;
}

.table-chooser-wrapper table td {
    cursor: pointer;
    border: 1px #ccc solid;
    height: 12px;
    min-width: 12px;
    line-height: 12px;
}

.table-chooser-wrapper table td.selected {
    background: highlight;
}


.tooltip {
    @apply relative inline-flex items-center cursor-pointer;
}

.tooltip-content {
    position: absolute;
    left: 60%;
    transform: translate(-50%, -100%);
    z-index: 4;
    @apply min-w-max bg-black text-white text-xs rounded mb-1.5 p-1.5 pointer-events-none;
    @apply transition-[opacity] duration-200 ease-out;
    opacity: 0;
}

.tooltip:hover .tooltip-content {
    opacity: 1;
}

.tooltip-content::after {
    content: "";
    position: absolute;
    left: 50%;
    top: 100%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: black transparent transparent transparent;
}

.tooltip-left > .tooltip-content {
    transform: translate(-50%, 140%);
    left: 0;
}

.tooltip-left > .tooltip-content::after {
    left: 60%;
    top: auto;
    bottom: 100%;
    border-color: transparent transparent black transparent;
}

.tooltip-content table th, .tooltip-content table td  {
    padding: 5px;
    border: 1px solid gray;
}

.tooltip-content table b  {
    color: red;
    font-weight: bold;
}


.dropdown {
    z-index: 5;
    @apply relative;
}

.dropdown-wrapper {
    @apply absolute py-4;
    animation: growOut 300ms ease-in-out forwards;
    transform-origin: center;
    display: none;
}

.dropdown:hover .dropdown-wrapper {
    display: block;
}

.dropdown-content {
    @apply py-1 shadow-md rounded focus:outline-none;
    @apply bg-white dark:bg-slate-700;
}

.dropdown-content::after {
    content: "";
    position: absolute;
    top: 2px;
    border: 7px solid transparent;
    @apply border-b-white dark:border-b-slate-700;
}

.dropdown hr {
    @apply border-t-0 border-b border-slate-100 dark:border-slate-600;
}

.dropdown-item {
    @apply items-center block pl-3 pr-5 my-1.5 py-1 cursor-pointer;
    @apply hover:bg-slate-200/80 dark:hover:text-white dark:hover:bg-slate-600/80;
    @apply transition duration-75 ease-out;
}

.dropdown-item > i {
    @apply mr-2 text-xs;
}


@keyframes growOut {
    0% {
        transform: scale(0.90);
    }
    50% {
        transform: scale(1.01);
    }
    100% {
        transform: scale(1);
    }
}

/* ========================================
   STYLES MOVED FROM HEAD.HTML
   ======================================== */

/* Modern Animation Keyframes */
@keyframes slide-up {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes glow {
    0% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
    100% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
}

/* Floating Animation */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

/* Modern Utility Classes */
.animate-slide-up {
    animation: slide-up 0.6s ease-out forwards;
}

.animate-float {
    animation: float 8s ease-in-out infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
}

/* Enhanced Glass Effect for Cards */
.glass-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.dark .glass-card {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.4);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* Hover effect for glass cards */
.glass-card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.dark .glass-card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Grid Pattern Background */
.bg-grid-pattern {
    background-image: radial-gradient(circle, #e5e7eb 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Unified Dropdown Menu Styles for All Components */
.dropdown-menu,
.language-dropdown-menu,
.share-dropdown-menu,
._dataSource .dropdown-menu {
    background: #ffffff;
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);
    border-radius: 12px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(8px) scale(0.95);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 9999;
    width: max-content;
    white-space: nowrap;
}

/* Specific scrollable dropdowns */
.language-dropdown-menu {
    max-height: 320px;
    overflow-y: auto;
}

.share-dropdown-menu {
    max-height: 280px;
    overflow-y: auto;
}

.dark .dropdown-menu,
.dark .language-dropdown-menu,
.dark .share-dropdown-menu,
.dark ._dataSource .dropdown-menu {
    background: #1e293b;
    border: 1px solid rgba(71, 85, 105, 0.4);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2), 0 2px 8px rgba(0, 0, 0, 0.12);
}

/* Show states for all dropdown types */
.group:hover .dropdown-menu,
._dataSource .group:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

/* Language and Share dropdown show states - only when NOT invisible */
.language-dropdown-menu:not(.invisible),
.share-dropdown-menu:not(.invisible) {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) scale(1) !important;
}

/* Language and Share dropdown hide states - when invisible */
.language-dropdown-menu.invisible,
.share-dropdown-menu.invisible {
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(8px) scale(0.95) !important;
}

/* Ensure dropdown stays visible when hovering over it */
.dropdown-menu:hover,
.language-dropdown-menu:hover,
.share-dropdown-menu:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

/* Unified dropdown item styles */
.dropdown-menu a,
.language-dropdown-menu a,
.share-dropdown-menu a,
._dataSource ._fromItem,
.search-content .items a {
    padding: 8px 12px;
    display: flex;
    align-items: center;
    transition: all 0.2s ease-in-out;
    border-radius: 8px;
    margin: 2px 4px;
    white-space: nowrap;
    font-size: 14px;
    font-weight: 500;
    color: rgb(71 85 105);
    text-decoration: none;
}

/* Dark mode text color */
.dark .dropdown-menu a,
.dark .language-dropdown-menu a,
.dark .share-dropdown-menu a,
.dark ._dataSource ._fromItem,
.dark .search-content .items a {
    color: rgb(203 213 225);
}

/* Hover states */
.dropdown-menu a:hover,
.language-dropdown-menu a:hover,
.share-dropdown-menu a:hover,
._dataSource ._fromItem:hover,
.search-content .items a:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(59, 130, 246, 0.04));
    transform: translateX(2px);
    color: rgb(59 130 246);
}

.dark .dropdown-menu a:hover,
.dark .language-dropdown-menu a:hover,
.dark .share-dropdown-menu a:hover,
.dark ._dataSource ._fromItem:hover,
.dark .search-content .items a:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.08));
    color: rgb(96 165 250);
}

/* Remove default list styling */
.dropdown-menu li,
.language-dropdown-menu li,
.share-dropdown-menu li {
    list-style: none;
    margin: 0;
    padding: 0;
}

/* Search dropdown unified styling */
.search-content .items {
    background: #ffffff;
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);
    border-radius: 12px;
    max-height: 384px;
    overflow-y: auto;
    z-index: 9999;
}

.dark .search-content .items {
    background: #1e293b;
    border: 1px solid rgba(71, 85, 105, 0.4);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2), 0 2px 8px rgba(0, 0, 0, 0.12);
}

/* Search item selected state */
.search-content .items a.selected {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.12), rgba(59, 130, 246, 0.06));
    color: rgb(59 130 246);
}

.dark .search-content .items a.selected {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.1));
    color: rgb(96 165 250);
}

/* Use system default scrollbars for dropdowns */
.language-dropdown-menu,
.share-dropdown-menu,
.search-content .items {
    scrollbar-width: auto; /* Firefox */
    -webkit-overflow-scrolling: touch; /* iOS */
}

/* Modern Card Hover Effects */
.modern-card {
    transition: all 0.3s ease;
}

.modern-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* ========================================
   Component Style Simplification
   ======================================== */

/* Recommendation Cards - Removed (reverted to inline styles) */

/* Stats Numbers Component */
.stats-section {
    @apply py-16 bg-transparent;
}

.stats-container {
    @apply max-w-7xl mx-auto;
}

.stats-grid {
    @apply grid grid-cols-2 md:grid-cols-4 gap-8;
}

.stats-item {
    @apply text-center;
}

.stats-number {
    @apply text-3xl font-bold mb-2;
}

.stats-label {
    @apply text-sm text-gray-600 dark:text-gray-400;
}

/* FAQ Component */
.faq-card {
    @apply glass-card rounded-xl overflow-hidden;
}

.faq-button {
    @apply w-full px-6 py-4 text-left flex items-center justify-between hover:bg-white/50 dark:hover:bg-slate-800/50 transition-colors;
}

.faq-title {
    @apply font-semibold text-gray-900 dark:text-white;
}

.faq-icon {
    @apply w-5 h-5 text-gray-500 dark:text-gray-400 transform transition-transform;
}

.faq-content {
    @apply px-6 pb-4;
}

.faq-text {
    @apply text-gray-600 dark:text-gray-300;
}

/* Footer Component */
.footer-main {
    @apply bg-gray-900 text-white py-16;
}

.footer-container {
    @apply container mx-auto;
}

.footer-grid {
    @apply grid lg:grid-cols-5 md:grid-cols-2 gap-8 mb-12;
}

.footer-brand {
    @apply lg:col-span-1;
}

.footer-logo {
    @apply flex items-center space-x-3 mb-6;
}

.footer-logo-icon {
    @apply flex items-center space-x-1;
}

.footer-logo-squares {
    @apply flex flex-col space-y-1;
}

.footer-logo-square {
    @apply w-2.5 h-2.5 bg-blue-500 rounded-sm;
}

.footer-logo-diamond {
    @apply w-2.5 h-2.5 bg-blue-400 rounded-sm transform rotate-45;
}

.footer-logo-text {
    @apply text-xl font-bold;
}

.footer-description {
    @apply text-gray-400 mb-6 leading-relaxed;
}

.footer-social {
    @apply flex space-x-4 mb-6;
}

.footer-social-link {
    @apply w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center transition-all duration-300 hover:scale-110;
}

/* Navbar Component */
.navbar-container {
    @apply max-container w-full bg-transparent pt-2;
}


.navbar-content {
    @apply flex justify-between items-center h-14;
}

.navbar-brand {
    @apply flex items-center space-x-4;
}

.navbar-logo {
    @apply flex items-center space-x-3;
}

.navbar-logo-icon {
    @apply flex items-center space-x-1 transition-transform duration-200;
}

.navbar-logo-squares {
    @apply flex flex-col space-y-1;
}

.navbar-logo-square {
    @apply w-2.5 h-2.5 bg-blue-500 rounded-sm;
}

.navbar-logo-diamond {
    @apply w-2.5 h-2.5 bg-blue-400 rounded-sm transform rotate-45;
}

.navbar-logo-text {
    @apply text-xl font-bold text-gray-900 dark:text-white transition-colors duration-200 hidden sm:inline;
}

/* Related Component */
.related-section {
    @apply py-16 bg-transparent;
}

.related-container {
    @apply max-w-7xl mx-auto;
}

.related-header {
    @apply text-center mb-12;
}

.related-title {
    @apply text-3xl font-bold text-gray-900 dark:text-white mb-4;
}

.related-description {
    @apply text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto;
}

.related-grid {
    @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4;
}

.related-card {
    @apply glass-card rounded-xl p-4 hover:shadow-xl dark:hover:shadow-2xl transition-all duration-300;
}

.related-link {
    @apply block;
}

.related-content {
    @apply flex items-center space-x-3;
}

.related-icon {
    @apply w-8 h-8 rounded-lg flex items-center justify-center;
}

.related-icon i {
    @apply text-white text-sm;
}

.related-text {
    @apply text-gray-900 dark:text-gray-100 transition-colors truncate;
}

.related-card:hover .related-text {
    @apply text-blue-600 dark:text-blue-400;
}

.extension-container {
    @apply max-w-7xl mx-auto;
}

.extension-main {
    @apply bg-gradient-to-br from-blue-600 to-purple-700 dark:from-blue-700 dark:to-purple-800 rounded-3xl p-12 text-white relative overflow-hidden;
}

.extension-bg-element {
    @apply absolute bg-white/10 rounded-full;
}

.extension-bg-top {
    @apply top-0 right-0 w-64 h-64 -translate-y-32 translate-x-32;
}

.extension-bg-bottom {
    @apply bottom-0 left-0 w-48 h-48 translate-y-24 -translate-x-24;
}

.extension-content {
    @apply relative z-10;
}

.extension-header {
    @apply text-center mb-12;
}

.extension-title {
    @apply text-3xl font-bold mb-4;
}

.extension-description {
    @apply text-lg opacity-90 max-w-3xl mx-auto;
}

.extension-grid {
    @apply grid lg:grid-cols-2 gap-12 items-center;
}

.extension-features {
    @apply space-y-6;
}

.extension-feature {
    @apply flex items-start space-x-4;
}

.extension-feature-icon {
    @apply flex-shrink-0 w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center;
}

.extension-feature-title {
    @apply text-lg font-semibold mb-2;
}

.extension-feature-text {
    @apply opacity-90;
}

.extension-downloads {
    @apply mt-8 space-y-4;
}

.extension-downloads-title {
    @apply text-lg font-semibold;
}

.extension-downloads-grid {
    @apply flex flex-wrap gap-4;
}

.extension-download-btn {
    @apply flex items-center space-x-3 bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3 hover:bg-white/30 transition-all duration-300 hover:scale-105;
}

.extension-download-name {
    @apply font-semibold;
}

.extension-download-store {
    @apply text-sm opacity-80;
}

/* Hero Section Component */
.hero-section {
    @apply py-16 bg-transparent relative overflow-hidden;
}

.hero-container {
    @apply max-w-7xl mx-auto relative;
}

.hero-content {
    @apply text-center;
}

.hero-title {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-gray-900 dark:text-white;
}

.hero-highlight {
    @apply bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent;
}

.hero-description {
    @apply text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed;
}

.hero-actions {
    @apply flex flex-col sm:flex-row gap-4 justify-center items-center mt-8;
}

.hero-btn-primary {
    @apply inline-flex items-center px-8 py-3.5 bg-gradient-to-r from-blue-600 via-blue-700 to-purple-600 hover:from-blue-700 hover:via-purple-600 hover:to-purple-700 text-white font-semibold rounded-full transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-blue-500/25 transform hover:-translate-y-0.5 active:translate-y-0 active:shadow-md;
    position: relative;
    overflow: hidden;
}

.hero-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.hero-btn-primary:hover::before {
    left: 100%;
}

.hero-btn-secondary {
    @apply inline-flex items-center px-8 py-3.5 bg-white/80 dark:bg-slate-800/80 border border-slate-200 dark:border-slate-600 text-slate-700 dark:text-slate-300 font-medium rounded-full hover:bg-white dark:hover:bg-slate-800 hover:border-slate-300 dark:hover:border-slate-500 hover:text-slate-900 dark:hover:text-slate-100 transition-all duration-300 backdrop-blur-sm shadow-sm hover:shadow-md transform hover:-translate-y-0.5 active:translate-y-0;
}

/* Editor Component */
.editor-section {
    @apply w-full py-4;
}

.editor-container {
    @apply max-w-7xl mx-auto;
}

.editor-main {
    @apply shadow dark:shadow-xl rounded-xl bg-white dark:bg-slate-800 p-3 md:p-6;
}

.editor-header {
    @apply flex flex-row justify-between items-center;
}

.editor-title-section {
    @apply flex items-center justify-center;
}

.editor-title-content {
    @apply flex items-center text-slate-700 dark:text-slate-300;
}

.editor-title {
    @apply font-semibold text-base;
}

.editor-actions {
    @apply flex flex-wrap items-center justify-center space-x-4;
}

.editor-tool-btn {
    @apply tooltip tooltip-left;
}

.editor-block {
    @apply flex flex-col h-full md:flex-row mt-5;
}

.editor-filters {
    @apply min-w-max space-y-6;
}

.editor-tools-grid {
    @apply w-full grid grid-cols-3 md:grid-cols-2 xl:grid-cols-3 gap-4;
}

/* Sponsors Component */
.sponsors-section {
    @apply my-10;
}

.sponsors-grid {
    @apply grid grid-cols-1 md:grid-cols-3 gap-6;
}

.sponsor-card {
    @apply relative rounded-xl p-5 border shadow hover:shadow-lg transition-all duration-300 hover:-translate-y-1;
}

.sponsor-card-pink {
    @apply bg-gradient-to-br from-pink-50 to-rose-50 dark:from-pink-900/20 dark:to-rose-900/20 border-pink-200 dark:border-pink-800 hover:border-pink-300 dark:hover:border-pink-600;
}

.sponsor-card-purple {
    @apply bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-200 dark:border-purple-800 hover:border-purple-300 dark:hover:border-purple-600;
}

.sponsor-card-ad {
    @apply bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border-2 border-dashed border-orange-200 dark:border-orange-700 hover:border-orange-300 dark:hover:border-orange-600;
}

.sponsor-header {
    @apply flex items-start justify-between mb-3;
}

.sponsor-info {
    @apply flex items-center space-x-4;
}

.sponsor-logo {
    @apply w-12 h-12 bg-white dark:bg-slate-800 rounded-xl p-2 shadow-md border border-gray-200 dark:border-gray-700;
}

.sponsor-logo-img {
    @apply w-full h-full rounded-lg;
}

.sponsor-logo-ad {
    @apply w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-xl flex items-center justify-center shadow-md;
}

.sponsor-title {
    @apply font-bold text-lg text-gray-900 dark:text-white;
}

.sponsor-icon {
    @apply w-6 h-6 text-gray-400 transition-colors;
}

.sponsor-icon-pink {
    @apply group-hover:text-pink-600 dark:group-hover:text-pink-400;
}

.sponsor-icon-purple {
    @apply group-hover:text-purple-600 dark:group-hover:text-purple-400;
}

.sponsor-description {
    @apply text-base text-gray-800 dark:text-gray-200 font-semibold;
}

/* Generator Component */
.generator-section {
    @apply w-full py-4;
}

.generator-container {
    @apply max-w-7xl mx-auto;
}

.generator-main {
    @apply shadow dark:shadow-xl rounded-xl bg-white dark:bg-slate-800;
}

.generator-header {
    @apply flex flex-row justify-between items-center p-3 md:p-6;
}

.generator-title-section {
    @apply flex items-center justify-center;
}

.generator-title-content {
    @apply flex items-center text-slate-700 dark:text-slate-300;
}

.generator-title {
    @apply font-semibold text-base;
}

.generator-twitter-btn {
    @apply hidden md:inline-flex items-center border border-black dark:border-white rounded-full ml-5 px-4 py-1 text-xs select-none text-black dark:text-white hover:bg-black dark:hover:bg-white hover:text-white dark:hover:text-black focus:outline-none;
}

.generator-actions {
    @apply flex flex-row items-center justify-center space-x-1 md:space-x-4;
}

.generator-sponsor-btn {
    @apply hidden md:inline items-center border border-red-600 rounded-full px-5 text-sm leading-8 select-none text-red-600 hover:bg-red-600 hover:text-white focus:outline-none;
}

/* Extension Ad Component */
.extension-ad {
    @apply flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3 lg:gap-0;
}

.extension-ad-message {
    @apply flex items-center space-x-3;
}

.extension-ad-text {
    @apply flex items-center gap-2;
}

.extension-ad-title {
    @apply font-semibold text-slate-800 dark:text-slate-200 text-sm md:text-base;
}

.extension-ad-downloads {
    @apply flex flex-wrap lg:flex-nowrap items-center gap-2 lg:space-x-2;
}

.extension-ad-btn {
    @apply flex items-center space-x-1 md:space-x-2 bg-white dark:bg-slate-800 px-2 md:px-3 py-1.5 md:py-2 rounded-full border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200;
}

.extension-ad-btn-blue {
    @apply hover:bg-blue-50 dark:hover:bg-slate-700 hover:border-blue-400 dark:hover:border-blue-500;
}

.extension-ad-btn-orange {
    @apply hover:bg-orange-50 dark:hover:bg-slate-700 hover:border-orange-400 dark:hover:border-orange-500;
}

.extension-ad-icon {
    @apply w-3 h-3 md:w-4 md:h-4;
}

.extension-ad-text-full {
    @apply text-xs md:text-sm font-medium text-slate-700 dark:text-slate-300 hidden md:inline;
}

.extension-ad-text-short {
    @apply text-xs font-medium text-slate-700 dark:text-slate-300 md:hidden;
}

.extension-ad-btn-blue:hover .extension-ad-text-full,
.extension-ad-btn-blue:hover .extension-ad-text-short {
    @apply text-blue-600 dark:text-blue-400;
}

.extension-ad-btn-orange:hover .extension-ad-text-full,
.extension-ad-btn-orange:hover .extension-ad-text-short {
    @apply text-orange-600 dark:text-orange-400;
}