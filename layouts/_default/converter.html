{{ define "main" }}
    <!-- <PERSON> Hero with Title, Description and Share -->
    {{ partial `page-hero` . }}

    <!-- Main Content - Left Sidebar + Right Content Layout -->
    <div class="max-container">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Left Sidebar -->
            <aside class="w-full lg:w-80 lg:flex-shrink-0 lg:sticky lg:top-8 lg:h-fit lg:max-h-screen lg:overflow-y-auto order-2 lg:order-1">
                {{ partial `sidebar.html` . }}
            </aside>

            <!-- Right Content -->
            <main class="flex-1 min-w-0 space-y-0 order-1 lg:order-2">
                {{ partial `convert/dataSource` . }}
                {{ partial `convert/editor` . }}
                {{ partial `convert/generator` . }}
                {{ partial `convert/related` . }}
                {{ partial `workflow-steps` . }}
                {{ partial `extension-section` . }}
                {{ partial `recommendations` . }}
                {{ partial `convert/faq` . }}
                {{ partial `stats-numbers` . }}
            </main>
        </div>
    </div>
{{ end }}
