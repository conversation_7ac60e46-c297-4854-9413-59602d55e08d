{{ define "main" }}
    <!-- <PERSON> Hero with Title, Description and Share -->
    {{ partial `page-hero` . }}

    <!-- Main Content - Left Sidebar + Right Content Layout -->
    <div class="max-container">
        <div class="sidebar-layout">
            <!-- Left Sidebar -->
            <aside class="sidebar-aside">
                {{ partial `sidebar.html` . }}
            </aside>

            <!-- Right Content -->
            <main class="sidebar-main space-y-0">
                {{ partial `convert/dataSource` . }}
                {{ partial `convert/editor` . }}
                {{ partial `convert/generator` . }}
                {{ partial `convert/related` . }}
                {{ partial `workflow-steps` . }}
                {{ partial `extension-section` . }}
                {{ partial `recommendations` . }}
                {{ partial `convert/faq` . }}
                {{ partial `stats-numbers` . }}
            </main>
        </div>
    </div>
{{ end }}
