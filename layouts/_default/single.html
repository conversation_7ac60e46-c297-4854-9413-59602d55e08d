{{ define "main" }}
    <div class="w-full pb-20">
        <h1 class="py-20 text-4xl text-center font-medium tracking-wide">{{- trim (partial `helpers/i18n_title` .) "\n" -}}</h1>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row gap-8">
                <!-- Left Sidebar -->
                <aside class="w-full lg:w-80 lg:flex-shrink-0 lg:sticky lg:top-8 lg:h-fit lg:max-h-screen lg:overflow-y-auto order-2 lg:order-1">
                    {{ partial `sidebar.html` . }}
                </aside>

                <!-- Right Content -->
                <main class="flex-1 min-w-0 order-1 lg:order-2">
                    <section class="mdContent shadow dark:shadow-xl rounded-xl bg-white dark:bg-slate-800 p-10">
                        {{ .Content }}
                    </section>
                </main>
            </div>
        </div>
    </div>
{{ end }}
