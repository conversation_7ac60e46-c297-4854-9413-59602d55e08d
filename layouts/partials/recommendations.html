<!-- Recommendations Section -->
<section class="py-16 bg-transparent">
    <div class="max-w-7xl mx-auto">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                {{ T "recommendations.section_title" }}
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                {{ T "recommendations.section_description" }}
            </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
            <!-- University Recommendation -->
            <div class="glass-card rounded-xl p-6 hover:shadow-xl transition-all duration-300 group hover:-translate-y-1">
                <div class="flex items-start space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                            {{ T "recommendations.cards.university_title" }}
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-3">
                            {{ T "recommendations.cards.university_description" }}
                        </p>
                        <a href="https://bcrf.biochem.wisc.edu/2020/01/28/tableconvert-com-free-tabular-data-formats-converter/"
                           target="_blank" rel="noopener noreferrer"
                           title="University of Wisconsin recommends TableConvert for data format conversion"
                           class="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium group-hover:underline">
                            {{ T "recommendations.cards.university_link" }}
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Facebook Recommendation -->
            <div class="glass-card rounded-xl p-6 hover:shadow-xl transition-all duration-300 group hover:-translate-y-1">
                <div class="flex items-start space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                            {{ T "recommendations.cards.facebook_title" }}
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-3">
                            {{ T "recommendations.cards.facebook_description" }}
                        </p>
                        <a href="https://www.facebook.com/story.php?story_fbid=1120029103468163&id=100063832518536&_rdr"
                           target="_blank" rel="noopener noreferrer"
                           title="See TableConvert recommendation on Facebook"
                           class="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium group-hover:underline">
                            {{ T "recommendations.cards.facebook_link" }}
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Twitter Recommendation -->
            <div class="glass-card rounded-xl p-6 hover:shadow-xl transition-all duration-300 group hover:-translate-y-1">
                <div class="flex items-start space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                            {{ T "recommendations.cards.twitter_title" }}
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-3">
                            {{ T "recommendations.cards.twitter_description" }}
                        </p>
                        <a href="https://x.com/xiaoying_eth/status/1716747968265777227"
                           target="_blank" rel="noopener noreferrer"
                           title="See TableConvert recommendation on Twitter"
                           class="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium group-hover:underline">
                            {{ T "recommendations.cards.twitter_link" }}
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
