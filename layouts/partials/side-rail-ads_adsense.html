<!-- AdSense Side Rail Ads Component -->
<!-- 左右两侧各2个120x300广告组件，总高度500px -->

<style>
  /* AdSense Side Rail Ads Container */
  .adsense-side-rail-ads {
    position: fixed;
    top: 50%;
    transform: translateY(-50%);
    z-index: 100;
    pointer-events: none;
  }

  .adsense-side-rail-ads .ad-container {
    pointer-events: auto;
    display: flex;
    flex-direction: column;
    gap: 0px;
  }

  /* Left Side Ad */
  .adsense-side-rail-ads.left {
    left: 10px;
  }

  /* Right Side Ad */
  .adsense-side-rail-ads.right {
    right: 10px;
  }

  /* 响应式设计 - 只在大屏幕显示 */
  @media only screen and (max-width: 1400px) {
    .adsense-side-rail-ads {
      display: none;
    }
  }

  /* AdSense广告样式优化 */
  .adsense-side-rail-ads .adsbygoogle {
    display: inline-block !important;
    width: 120px !important;
    height: 300px !important;
  }

  /* 关闭按钮样式 */
  .adsense-side-rail-ads .close-btn {
    position: absolute;
    top: -8px;
    width: 20px;
    height: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 50%;
    cursor: pointer;
    display: none; /* 默认隐藏 */
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #666;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 999; /* 提高z-index确保在最上层 */
    transition: all 0.2s ease;
    opacity: 0;
    transform: scale(0.8);
    pointer-events: auto; /* 确保按钮可以接收点击事件 */
  }

  .adsense-side-rail-ads .close-btn:hover {
    background: #f5f5f5;
    color: #333;
    transform: scale(1.1);
  }

  .adsense-side-rail-ads.left .close-btn {
    right: -8px;
  }

  .adsense-side-rail-ads.right .close-btn {
    left: -8px;
  }

  /* hover时检查广告内容并显示关闭按钮 */
  .adsense-side-rail-ads:hover .close-btn {
    display: flex;
    opacity: 1;
    transform: scale(1);
  }

  /* 隐藏状态 */
  .adsense-side-rail-ads.hidden {
    display: none !important;
  }
</style>

<!-- Left Side Rail Ads -->
<div class="adsense-side-rail-ads left" id="adsense-left-rail" onmouseover="showCloseButtonIfHasAds(this)">
  <button class="close-btn" onclick="closeSideRailAd('adsense-left-rail'); event.stopPropagation();" title="Close Ad">×</button>
  <div class="ad-container">
    <!-- tableconvert_side-rail-ads-01 -->
    <ins class="adsbygoogle"
     style="display:block"
     data-ad-client="ca-pub-8691406134231910"
     data-ad-slot="7852281658"
     data-ad-format="auto"></ins>

    <!-- tableconvert_side-rail-ads-02 -->
    <ins class="adsbygoogle"
     style="display:block"
     data-ad-client="ca-pub-8691406134231910"
     data-ad-slot="1677755547"
     data-ad-format="auto"></ins>
</div>

<!-- Right Side Rail Ads -->
<div class="adsense-side-rail-ads right" id="adsense-right-rail" onmouseover="showCloseButtonIfHasAds(this)">
  <button class="close-btn" onclick="closeSideRailAd('adsense-right-rail'); event.stopPropagation();" title="Close Ad">×</button>
  <div class="ad-container">
    <!-- tableconvert_side-rail-ads-03 -->
    <ins class="adsbygoogle"
         style="display:inline-block;width:120px;height:300px"
         data-ad-client="ca-pub-8691406134231910"
         data-ad-slot="2599954971"></ins>

    <!-- tableconvert_side-rail-ads-04 -->
    <ins class="adsbygoogle"
         style="display:inline-block;width:120px;height:300px"
         data-ad-client="ca-pub-8691406134231910"
         data-ad-slot="8973791638"></ins>
  </div>
</div>

<script>
// Side Rail Ads关闭功能 - 直接删除DOM节点
function closeSideRailAd(adId) {
  const adElement = document.getElementById(adId);
  if (adElement) {
    adElement.remove(); // 直接删除DOM节点，简单高效
  }
}

// hover时检查是否有广告内容，决定是否显示关闭按钮
function showCloseButtonIfHasAds(container) {
  const closeBtn = container.querySelector('.close-btn');
  if (!closeBtn) return;

  // 如果关闭按钮已经显示，不再重复判断
  if (closeBtn.style.display === 'flex') return;

  // 检查AdSense广告是否有内容 - 使用data属性判断
  const ads = container.querySelectorAll('.adsbygoogle');
  let hasAds = false;

  ads.forEach(function(ad) {
    // 只使用 data-ad-status 的 filled 状态来判断广告是否已填充
    const adStatus = ad.getAttribute('data-ad-status');

    if (adStatus === 'filled') {
      hasAds = true;
    }
  });

  // 如果有广告内容，显示关闭按钮；否则隐藏
  closeBtn.style.display = hasAds ? 'flex' : 'none';
}
</script>