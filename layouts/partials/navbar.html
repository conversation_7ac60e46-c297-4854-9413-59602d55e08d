<!-- Modern Navigation - Transparent -->
<nav class="navbar-container">
    <div class="navbar-content">
        <!-- Left Column: Logo Section -->
        <div class="navbar-brand">
            <a href="{{ "" | relLangURL }}" class="navbar-logo" title="{{ T "site.name" }} - {{ T "site.subtitle" }}">
                <!-- Simple Table Icon -->
                <div class="navbar-logo-icon">
                    <!-- Left column of squares -->
                    <div class="navbar-logo-squares">
                        <div class="navbar-logo-square"></div>
                        <div class="navbar-logo-square"></div>
                    </div>
                    <!-- Right column with diamond and square -->
                    <div class="navbar-logo-squares items-center">
                        <div class="navbar-logo-diamond"></div>
                        <div class="navbar-logo-square"></div>
                    </div>
                </div>
                <span class="navbar-logo-text">
                    {{ T "site.name" }}
                </span>
            </a>
            <a href="#" title="What's News" data-micromodal-trigger="changelog-modal"
                class="text-xs bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 px-2 py-1 rounded-full hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors hidden sm:inline-block">
                {{ (index $.Site.Data.changelog 0).version }}
            </a>
        </div>

        <!-- Right Column: Search + Navigation + Actions -->
        <div class="flex-1 flex items-center justify-between ml-8">
            <!-- Left Part: Search + Navigation Links -->
            <div class="flex items-center space-x-4">
                <!-- Search -->
                <div class="hidden lg:block">
                    {{ partialCached "search.html" . }}
                </div>
            </div>

            <!-- Right Part: Action Buttons -->
            <div class="flex items-center space-x-1">
                <!-- Navigation Links - Hide when space is limited -->
                <div class="hidden xl:flex items-center space-x-4">
                    <a href="{{ "latex-generator.html" | relLangURL }}" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors py-1.5 px-3 rounded-full text-sm whitespace-nowrap" title="{{ printf (T "title.generator") "LaTeX Table" }}">
                        {{- $toAlias := T (printf "converters.%s.alias" "LaTeX") -}}
                        {{ printf (T "title.generator") $toAlias }}
                    </a>
                    <a class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors py-1.5 px-3 rounded-full text-sm whitespace-nowrap" href="#extension-section" title="{{ T "navbar.extension" }}">
                        {{ T "navbar.extension" }}
                    </a>
                    <a id="api-portal" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors py-1.5 px-3 rounded-full text-sm whitespace-nowrap"
                        href="{{- (printf "/api/#post-/convert/%s-to-%s" (.Params.From | lower) (.Params.To | lower)) | relLangURL -}}" title="{{ T "navbar.api" }}">
                        {{ T "navbar.api" }}
                    </a>
                </div>

                <!-- Language Dropdown -->
                {{ partial `language-dropdown` . }}

                <!-- Share Dropdown -->
                {{ partial `share-dropdown` . }}

                <!-- Dark Mode Toggle -->
                <button id="darkMode" aria-label="Dark Mode"
                        class="py-1.5 px-3 lg:px-4 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200 min-h-[44px]">
                    <i class="icon icon-light text-base lg:text-lg"></i>
                </button>
            </div>
        </div>
    </div>
</nav>




