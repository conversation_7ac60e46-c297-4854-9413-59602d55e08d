<!-- Modern Browser Extension Section -->
<section id="extension-section">
    <div class="extension-container">
        <div class="extension-main">
            <!-- Background Elements -->
            <div class="extension-bg-element extension-bg-top"></div>
            <div class="extension-bg-element extension-bg-bottom"></div>

            <div class="extension-content">
                <div class="extension-header">
                    <h2 class="extension-title">{{ T "extension.section_title" }}</h2>
                    <p class="extension-description">{{ T "extension.section_description" }}</p>
                </div>

                <div class="extension-grid">
                    <!-- Left: Features -->
                    <div>
                        <div class="extension-features">
                            <div class="extension-feature">
                                <div class="extension-feature-icon">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="extension-feature-title">{{ T "extension.features.extraction_title" }}</h3>
                                    <p class="extension-feature-text">{{ T "extension.features.extraction_description" }}</p>
                                </div>
                            </div>

                            <div class="extension-feature">
                                <div class="extension-feature-icon">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="extension-feature-title">{{ T "extension.features.formats_title" }}</h3>
                                    <p class="extension-feature-text">{{ T "extension.features.formats_description" }}</p>
                                </div>
                            </div>

                            <div class="extension-feature">
                                <div class="extension-feature-icon">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="extension-feature-title">{{ T "extension.features.detection_title" }}</h3>
                                    <p class="extension-feature-text">{{ T "extension.features.detection_description" }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Download Buttons -->
                        <div class="extension-downloads">
                            <div class="extension-downloads-grid">
                                <!-- Chrome -->
                                <a href="https://chromewebstore.google.com/detail/table-extractor/flnkfbknlpooopiejmcejcldfcffkkcf"
                                   target="_blank" rel="noopener"
                                   title="Download Table Extractor for Chrome - Extract tables from any webpage"
                                   class="extension-download-btn">
                                    <img src="/icons/chrome.svg" alt="Chrome" class="w-6 h-6" />
                                    <div>
                                        <div class="extension-download-name">Chrome</div>
                                        <div class="extension-download-store">Web Store</div>
                                    </div>
                                </a>

                                <!-- Firefox -->
                                <a href="https://addons.mozilla.org/en-US/firefox/addon/table-extractor-tableconvert/"
                                   target="_blank" rel="noopener"
                                   title="Download Table Extractor for Firefox - Extract tables from any webpage"
                                   class="extension-download-btn">
                                    <img src="/icons/firefox.svg" alt="Firefox" class="w-6 h-6" />
                                    <div>
                                        <div class="extension-download-name">Firefox</div>
                                        <div class="extension-download-store">Add-ons</div>
                                    </div>
                                </a>

                                <!-- Edge -->
                                <a href="https://microsoftedge.microsoft.com/addons/detail/table-detection-extract/pckiffffenmnlnmhmbioajfdpoenahkj"
                                   target="_blank" rel="noopener"
                                   title="Download Table Extractor for Edge - Extract tables from any webpage"
                                   class="extension-download-btn">
                                    <img src="/icons/edge.svg" alt="Edge" class="w-6 h-6" />
                                    <div>
                                        <div class="extension-download-name">Edge</div>
                                        <div class="extension-download-store">Add-ons</div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Right: Demo/Screenshot -->
                    <div class="relative">
                        <div class="bg-white/20 backdrop-blur-sm rounded-xl p-6 border border-white/30">
                            <div class="flex items-center space-x-2 mb-4">
                                <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                                <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                <div class="flex-1 bg-white/20 rounded px-3 py-1 text-sm">
                                    tableconvert.com
                                </div>
                            </div>

                            <!-- Demo Table -->
                            <div class="relative">
                                <div class="w-full text-sm border border-white/30 rounded-lg overflow-hidden bg-white/10">
                                    <!-- Table Header -->
                                    <div class="bg-white/20 grid grid-cols-3 border-b border-white/30">
                                        <div class="px-3 py-2 font-medium text-left border-r border-white/30">Product</div>
                                        <div class="px-3 py-2 font-medium text-left border-r border-white/30">Price</div>
                                        <div class="px-3 py-2 font-medium text-left">Stock</div>
                                    </div>
                                    <!-- Table Body -->
                                    <div class="bg-white/10">
                                        <div class="grid grid-cols-3 border-b border-white/30">
                                            <div class="px-3 py-2 border-r border-white/30">Laptop</div>
                                            <div class="px-3 py-2 border-r border-white/30">$999</div>
                                            <div class="px-3 py-2">15</div>
                                        </div>
                                        <div class="grid grid-cols-3 border-b border-white/30">
                                            <div class="px-3 py-2 border-r border-white/30">Mouse</div>
                                            <div class="px-3 py-2 border-r border-white/30">$29</div>
                                            <div class="px-3 py-2">50</div>
                                        </div>
                                        <div class="grid grid-cols-3">
                                            <div class="px-3 py-2 border-r border-white/30">Keyboard</div>
                                            <div class="px-3 py-2 border-r border-white/30">$79</div>
                                            <div class="px-3 py-2">25</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Hover Icon Simulation with Project Icon -->
                                <div class="absolute -top-2 -right-2 w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg animate-pulse border-2 border-blue-400">
                                    <i class="icon icon-logo text-blue-600 text-lg"></i>
                                </div>
                            </div>

                            <div class="mt-4 text-center text-sm opacity-90">
                                {{ T "extension.hover_tip" }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
