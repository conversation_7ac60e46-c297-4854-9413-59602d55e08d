<!-- FAQ Section - Exact Match with Demo -->
<section class="py-16 bg-transparent">
    <div class="max-w-4xl mx-auto">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">{{ T "faq.section_title" }}</h2>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">{{ T "faq.section_description" }}</p>
        </div>

        <div class="space-y-4">
            {{ if eq .Params.Layout "converter" }}
                <!-- Source Format FAQ -->
                <div class="faq-card">
                    <button class="faq-button">
                        <span class="faq-title">{{ printf (T "faq.what") .Params.from }}</span>
                        <svg class="faq-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-content" style="display: none;">
                        <p class="faq-text">{{ (T (printf "converters.%s.what" .Params.from)) }}</p>
                    </div>
                </div>
            {{ end }}

            <!-- Target Format FAQ -->
            <div class="faq-card">
                <button class="faq-button">
                    <span class="faq-title">{{ printf (T "faq.what") .Params.to }}</span>
                    <svg class="faq-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div class="faq-content" style="display: none;">
                    <p class="faq-text">{{ (T (printf "converters.%s.what" .Params.to)) }}</p>
                </div>
            </div>

            <!-- How to Convert FAQ -->
            <div class="faq-card">
                <button class="faq-button">
                    <span class="faq-title">{{ T "faq.howto_convert.question" (partial `helpers/i18n_title` .) }}</span>
                    <svg class="faq-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div class="faq-content" style="display: none;">
                    <p class="faq-text">{{ T "faq.howto_convert.answer" .Params }}</p>
                </div>
            </div>

            <!-- Security FAQ -->
            <div class="faq-card">
                <button class="faq-button">
                    <span class="faq-title">{{ T "faq.security.question" }}</span>
                    <svg class="faq-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div class="faq-content" style="display: none;">
                    <p class="faq-text">{{ T "faq.security.answer" }}</p>
                </div>
            </div>

            <!-- Free Usage FAQ -->
            <div class="faq-card">
                <button class="faq-button">
                    <span class="faq-title">{{ T "faq.free.question" }}</span>
                    <svg class="faq-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div class="faq-content" style="display: none;">
                    <p class="faq-text">{{ T "faq.free.answer" }}</p>
                </div>
            </div>

            <!-- File Size FAQ -->
            <div class="faq-card">
                <button class="faq-button">
                    <span class="faq-title">{{ T "faq.filesize.question" }}</span>
                    <svg class="faq-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div class="faq-content" style="display: none;">
                    <p class="faq-text">{{ T "faq.filesize.answer" }}</p>
                </div>
            </div>
        </div>
    </div>
</section>


