<section id="dataSource" class="w-full pb-4">
    <div class="max-w-7xl mx-auto">
        <div class="_dataSource shadow dark:shadow-xl rounded-xl bg-white dark:bg-slate-800 p-3 md:p-6">
    <div class="flex flex-col md:flex-row md:justify-between md:items-center space-y-4 md:space-y-0">
        <div class="flex items-center justify-center">
            <div class="flex items-center text-slate-700 dark:text-slate-300">
                <div id="DataSource" class="flex items-center">
                    {{ $from := index .Site.Data.converters .Params.From }}
                    <i class="icon {{ $from.icon }} {{ $from.bg }} icon-rounded-full mr-3"></i>
                    <h2 class="font-semibold text-base">{{ T "converter.dataSource.title" }}</h2>
                </div>
                <div class="group inline-block ml-3">
                    <button
                        class="flex items-center outline-none focus:outline-none border-blue-500 text-blue-600 font-bold px-3 py-1 bg-white dark:bg-slate-800 rounded-full">
                        <span class="pr-1">{{ (T (printf "converters.%s.alias" .Params.From)) }}</span>
                        <i class="icon icon-unfold"></i>
                    </button>
                    <ul class="dropdown-menu absolute py-1.5">
                        {{ $to := .Params.To }}
                        {{ range $index, $item := .Site.Data.converters.fromList }}
                            {{ $converter := index $.Site.Data.converters $item }}
                            {{ $url := (printf "%s-to-%s.html" $item $to) | lower | relLangURL }}
                            {{ $linkTitle := printf (T "title.converter") $item $to }}
                            <li class="{{ if gt $index 0 }}border-t border-slate-100 dark:border-slate-700/50{{ end }}">
                                <a class="_fromItem flex items-center px-3 py-2 text-sm hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-all duration-200 group"
                                   href="{{ $url }}"
                                   title="{{ $linkTitle }}">
                                    <div class="flex items-center justify-center w-4 h-4 rounded {{ $converter.bg }} mr-3 flex-shrink-0 group-hover:scale-105 transition-transform duration-200">
                                        <i class="icon {{ $converter.icon }} text-white" style="font-size: 10px;"></i>
                                    </div>
                                    <span class="font-medium text-slate-700 dark:text-slate-200 group-hover:text-slate-900 dark:group-hover:text-white flex-1">{{ (T (printf "converters.%s.alias" $item)) }}</span>
                                </a>
                            </li>
                        {{ end }}
                    </ul>
                </div>
                <div class="choose-sheet hidden flex items-center">
                    <div class="w-px h-5 mx-3 bg-slate-300 dark:bg-slate-100/20 flex-none"></div>
                    <span class="">{{ T "converter.dataSource.excel.sheet" }}</span>
                </div>
                <div class="choose-sheet hidden group inline-block">
                    <button
                        class="flex items-center outline-none focus:outline-none border-blue-500 text-blue-600 px-3 py-1 bg-white dark:bg-slate-800 rounded-full">
                        <span class="pr-1 sheet-default w-16 truncate">{{ T "converter.dataSource.excel.none" }}</span>
                        <i class="icon icon-unfold"></i>
                    </button>
                    <div class="sheet-list group-hover:scale-100"></div>
                </div>
                    {{/*  <a href="https://chromewebstore.google.com/detail/iphmldfihamojndfpkfnllbmheclpcde" target="_blank"
                       class="ml-3 inline-block text-sm underline text-blue-700 hover:opacity-80">🔥 Fiona AI -
                        <b>ChatGPT</b> SidePanel + GPTs & GPT-4o</a>  */}}
            </div>
        </div>
        <div class="flex flex-wrap items-center justify-center space-x-2 md:space-x-4">
            <button id="example"
                    class="btn-modern flex items-center border border-transparent rounded-full px-2 py-1.5 md:px-5 md:py-2 text-xs md:text-sm select-none text-white focus:outline-none transition-all duration-200">
                <i class="hidden sm:inline text-sm icon icon-example mr-4"></i>
                {{ T "converter.dataSource.example" }}
            </button>
            <button id="select-file"
                    class="flex items-center px-2 py-1.5 md:px-5 md:py-2 text-xs md:text-sm border border-blue-500 rounded-full text-blue-600 bg-white dark:bg-slate-800 hover:bg-blue-500 hover:text-white transition-all duration-200 select-none focus:outline-none">
                {{ T "converter.dataSource.upload" }}
            </button>
            <div id="extract-panel" class="inline-block dropdown relative">
                <button type="button" data-toggle="dropdown"
                        class="flex items-center px-2 py-1.5 md:px-5 md:py-2 text-xs md:text-sm border border-blue-500 rounded-full text-blue-600 bg-white dark:bg-slate-800 hover:bg-blue-500 hover:text-white transition-all duration-200 select-none focus:outline-none">
                    {{ T "converter.dataSource.extract.enter" }}
                </button>
                <div data-close
                     class="dropdown-panel invisible p-0 mt-3 absolute right-0 min-w-max bg-white dark:bg-slate-800 rounded-md shadow dark:shadow-xl dark:border-slate-700 transform opacity-0 scale-95 transition-all duration-200 ease-out">
                    <div class="p-6 space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0 w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                                <i class="icon icon-extract text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h3 class="text-sm font-medium text-slate-900 dark:text-slate-100">{{ T "converter.dataSource.extract.enter" }}</h3>
                                <p class="text-xs text-slate-500 dark:text-slate-400 mt-1">{{ T "converter.dataSource.extract.intro" }}</p>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <div class="relative">
                                <input id="input-url"
                                       class="w-full pl-5 pr-4 py-3 text-sm border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 placeholder-slate-400 dark:placeholder-slate-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 outline-none"
                                       placeholder="https://example.com/path/to/{{ .Params.From | lower }}-data" />
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="icon icon-link text-slate-400 dark:text-slate-500 text-sm"></i>
                                </div>
                            </div>

                            <button id="extract"
                                    class="btn-modern w-full flex items-center justify-center px-4 py-3 text-sm font-medium text-white rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 space-x-2">
                                <i class="flex items-center justify-center icon icon-extract"></i>
                                <i class="hidden flex items-center justify-center icon icon-spinner animate-spin"></i>
                                <span>{{ printf (T "converter.dataSource.extract.btn") (.Params.from) | safeHTML }}</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="mt-5">
        <div class="file relative border-4 border-dashed border-transparent w-full bg-slate-700 dark:bg-slate-700 rounded-3xl p-0 transition-all duration-300 ease-in-out group">
            <!-- 主要内容区域 -->
            <div class="relative p-6">
                <!-- 主输入区域 -->
                <div class="relative">
                    <!-- 中央图标和文字 -->
                    <div class="absolute inset-0 flex flex-col items-center justify-center pointer-events-none text-center space-y-3 opacity-50 group-hover:opacity-70" id="empty-state">
                        <div class="space-y-1">
                            <p class="text-xl text-white dark:text-white">
                                {{ printf (T "converter.dataSource.placeholder") (T (printf "converters.%s.alias" .Params.From)) .Params.From }}
                            </p>
                        </div>
                    </div>

                    <!-- Textarea -->
                    <textarea
                        class="relative text-sm w-full bg-transparent border-0 text-white dark:text-white font-mono focus:outline-none resize-none placeholder-transparent"
                        id="importContent"
                        rows="8"
                        placeholder="{{ printf (T "converter.dataSource.placeholder") (T (printf "converters.%s.alias" .Params.From)) .Params.From }}"
                        autocomplete="off"
                        autocorrect="off"
                        autocapitalize="off"
                        spellcheck="false"></textarea>
                </div>

                <!-- 底部格式显示 -->
                <div class="mt-4 flex items-center justify-end text-xs text-slate-400 dark:text-slate-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div class="flex items-center space-x-1">
                        {{ $from := index .Site.Data.converters .Params.From }}
                        {{ $formats := split $from.accept "," }}
                        {{ range $index, $format := $formats }}
                            {{ if gt $index 0 }}<span>,</span>{{ end }}
                            <span class="text-slate-300 dark:text-slate-300">{{ trim $format " " }}</span>
                        {{ end }}
                    </div>
                </div>
            </div>

            {{ $from := index .Site.Data.converters .Params.From }}
            <input id="file-input" type="file" accept="{{ $from.accept }}" class="hidden" />
        </div>
    </div>

    <!-- Extension Advertisement -->
    <div class="mt-4 pt-2">
        {{ partial `convert/extension-ad` . }}
    </div>
</section>
