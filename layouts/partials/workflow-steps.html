<!-- How to Convert Section - Exact Match with Demo -->
<section class="py-16 bg-transparent">
    <div class="max-w-7xl mx-auto">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                {{ T "howto.section_title" (partial `helpers/i18n_title` .)}}
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                {{- $fromAlias := T (printf "converters.%s.from_alias" .Params.From) -}}
                {{- $toAlias := T (printf "converters.%s.to_alias" .Params.To) -}}
                {{ if eq .Params.Layout "converter" }}
                    {{ T "howto.converter_description" (dict "from" $fromAlias "to" $toAlias ) }}
                {{ else }}
                    {{ T "howto.generator_description" (dict "to" $toAlias ) }}
                {{ end }}
            </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
            <!-- Step 1 -->
            <div class="text-center group">
                <div class="w-20 h-20 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <span class="text-2xl font-bold text-white">1</span>
                </div>
                <h3 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">{{ T "sidebar.dataSource.title" }}</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    {{ if eq .Params.Layout "converter" }}
                        {{ T (printf "converters.%s.step1" .Params.From) | default (printf (T "sidebar.dataSource.description.converter") (T (printf "converters.%s.alias" .Params.From)) (T (printf "converters.%s.alias" .Params.To))) }}
                    {{ else }}
                        {{ T (printf "converters.%s.step1" .Params.To) | default (T "sidebar.dataSource.description.generator") }}
                    {{ end }}
                </p>
            </div>

            <!-- Step 2 -->
            <div class="text-center group">
                <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <span class="text-2xl font-bold text-white">2</span>
                </div>
                <h3 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">{{ T "sidebar.tableEditor.title" }}</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    {{ printf (T "howto.step2") .Params.To }}
                </p>
            </div>

            <!-- Step 3 -->
            <div class="text-center group">
                <div class="w-20 h-20 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <span class="text-2xl font-bold text-white">3</span>
                </div>
                <h3 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">{{ T "sidebar.tableGenerator.title" }}</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    {{ if eq .Params.Layout "converter" }}
                        {{ T (printf "converters.%s.step3" .Params.To) | default (printf (T "sidebar.tableGenerator.description.converter") (T (printf "converters.%s.alias" .Params.To))) }}
                    {{ else }}
                        {{ T (printf "converters.%s.step3" .Params.To) | default (printf (T "sidebar.tableGenerator.description.generator") (T (printf "converters.%s.alias" .Params.To))) }}
                    {{ end }}
                </p>
            </div>
        </div>
    </div>
</section>
