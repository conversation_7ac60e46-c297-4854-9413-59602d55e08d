<!-- Mixed Side Rail Ads Component -->
<!-- 左侧：AdSense广告1和2，右侧：BSA广告1 -->

<style>
  /* Mixed Side Rail Ads Container */
  .mixed-side-rail-ads {
    position: fixed;
    top: 50%;
    transform: translateY(-50%);
    z-index: 100;
    pointer-events: none;
  }

  .mixed-side-rail-ads .ad-container {
    pointer-events: auto;
  }

  /* Left Side Ad Container */
  .mixed-side-rail-ads.left .ad-container {
    display: flex;
    flex-direction: column;
    gap: 0px;
  }

  /* Left Side Ad */
  .mixed-side-rail-ads.left {
    left: 10px;
  }

  /* Right Side Ad */
  .mixed-side-rail-ads.right {
    right: 10px;
  }

  /* 响应式设计 - 只在大屏幕显示 */
  @media only screen and (max-width: 1400px) {
    .mixed-side-rail-ads {
      display: none;
    }
  }

  /* AdSense广告样式优化 */
  .mixed-side-rail-ads .adsbygoogle {
    display: inline-block !important;
    width: 120px !important;
    height: 300px !important;
  }

  /* BSA广告样式 */
  @media only screen and (min-width: 0px) and (min-height: 0px) {
    .mixed-side-rail-ads div[id^="bsa-zone_1753789692127-2_123456"] {
      min-width: 0px;
      min-height: 0px;
    }
  }
  @media only screen and (min-width: 1000px) and (min-height: 0px) {
    .mixed-side-rail-ads div[id^="bsa-zone_1753789692127-2_123456"] {
      min-width: 120px;
      min-height: 600px;
    }
  }

  /* 关闭按钮样式 */
  .mixed-side-rail-ads .close-btn {
    position: absolute;
    top: -8px;
    width: 20px;
    height: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 50%;
    cursor: pointer;
    display: none; /* 默认隐藏 */
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #666;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 999; /* 提高z-index确保在最上层 */
    transition: all 0.2s ease;
    opacity: 0;
    transform: scale(0.8);
    pointer-events: auto; /* 确保按钮可以接收点击事件 */
  }

  .mixed-side-rail-ads .close-btn:hover {
    background: #f5f5f5;
    color: #333;
    transform: scale(1.1);
  }

  .mixed-side-rail-ads.left .close-btn {
    right: -8px;
  }

  .mixed-side-rail-ads.right .close-btn {
    left: -8px;
  }

  /* hover时检查广告内容并显示关闭按钮 */
  .mixed-side-rail-ads:hover .close-btn {
    display: flex;
    opacity: 1;
    transform: scale(1);
  }

  /* 隐藏状态 */
  .mixed-side-rail-ads.hidden {
    display: none !important;
  }
</style>


<!-- Left Side Rail Ads (AdSense) -->
<div class="mixed-side-rail-ads left" id="mixed-left-rail" onmouseover="showCloseButtonIfHasAds(this)">
  <button class="close-btn" onclick="closeSideRailAd('mixed-left-rail'); event.stopPropagation();" title="Close Ad">×</button>
  <div class="ad-container">
    <!-- tableconvert_side-rail-ads-01 -->
    <ins class="adsbygoogle"
     style="display:block"
     data-ad-client="ca-pub-8691406134231910"
     data-ad-slot="7852281658"
     data-ad-format="auto"></ins>

    <!-- tableconvert_side-rail-ads-02 -->
    <ins class="adsbygoogle"
     style="display:block"
     data-ad-client="ca-pub-8691406134231910"
     data-ad-slot="1677755547"
     data-ad-format="auto"></ins>
  </div>
</div>

<!-- Right Side Rail Ads (BSA) -->
<div class="mixed-side-rail-ads right" id="mixed-right-rail" onmouseover="showCloseButtonIfHasAds(this)">
  <button class="close-btn" onclick="closeSideRailAd('mixed-right-rail'); event.stopPropagation();" title="Close Ad">×</button>
  <div class="ad-container">
    <!-- Tableconvert_S2S_120x600_1_ROS -->
    <div id="bsa-zone_1753789692127-2_123456"></div>
  </div>
</div>

<script>
// Side Rail Ads关闭功能 - 直接删除DOM节点
function closeSideRailAd(adId) {
  const adElement = document.getElementById(adId);
  if (adElement) {
    adElement.remove(); // 直接删除DOM节点，简单高效
  }
}

// hover时检查是否有广告内容，决定是否显示关闭按钮
function showCloseButtonIfHasAds(container) {
  const closeBtn = container.querySelector('.close-btn');
  if (!closeBtn) return;

  // 如果关闭按钮已经显示，不再重复判断
  if (closeBtn.style.display === 'flex') return;

  let hasAds = false;

  // 检查AdSense广告 - 只使用 data-ad-status 判断
  const adsenseAds = container.querySelectorAll('.adsbygoogle');
  adsenseAds.forEach(function(ad) {
    // 只使用 data-ad-status 的 filled 状态来判断广告是否已填充
    const adStatus = ad.getAttribute('data-ad-status');

    if (adStatus === 'filled') {
      hasAds = true;
    }
  });

  // 检查BSA广告
  const bsaAds = container.querySelectorAll('[id^="bsa-zone_"]');
  bsaAds.forEach(function(ad) {
    if (ad.children.length > 0 || ad.innerHTML.trim() !== '') {
      hasAds = true;
    }
  });

  // 如果有广告内容，显示关闭按钮；否则隐藏
  closeBtn.style.display = hasAds ? 'flex' : 'none';
}
</script>
