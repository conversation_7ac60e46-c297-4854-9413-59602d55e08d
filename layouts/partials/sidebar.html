{{/*<div class="sticky h-screen top-8 space-y-8">*/}}
<div class="space-y-8">
    <div class="">
        <h1 class="mb-3 text-xl dark:text-slate-300 font-bold">{{ partial `helpers/i18n_title` . }}</h1>
        <div class="text-slate-600 mt-4 text-sm">
            {{- partial `helpers/i18n_short` . -}}
            {{- if eq .Params.Layout "converter" -}}
                {{- (printf (T "site.apiSupport") (.Params.From | lower) (.Params.To | lower) (partial `helpers/i18n_title` .)) | safeHTML -}}
            {{- end -}}
        </div>
        {{ partial `share` . }}
        <!--    <div data-ea-publisher="tableconvertcom" data-ea-type="text"></div>-->
        <!--    <style> .ea-content{ margin: 1em 0 0 0 !important; } .ea-callout{ margin: 1em 0 0 0 !important;} </style>-->
    </div>
    {{ if or (eq .Params.Layout "converter") (eq .Params.Layout "generator") }}
        <div class="space-y-8">
            {{ $step := 0 }}
            {{ if eq .Params.Layout "converter" }}
                <div class="">
                    <div class="flex items-center">
                        <div class="w-7 h-7 border-2 border-blue-500 rounded-full flex items-center">
                            <span class="text-center w-full text-blue-600">{{ $step = add $step 1 }} {{ $step }}</span>
                        </div>
                        <span
                            class="font-bold ml-3 text-slate-700 dark:text-slate-400">{{ T "sidebar.dataSource.title" }}</span>
                    </div>
                    <div
                        class="text-slate-600 mt-4 text-sm">{{ printf (T "sidebar.dataSource.description.converter") (T (printf "converters.%s.alias" .Params.From)) (T (printf "converters.%s.alias" .Params.to)) }}</div>
                </div>
            {{ end }}
            <div class="">
                <div class="flex items-center">
                    <div class="w-7 h-7 border-2 border-blue-500 rounded-full flex items-center">
                        <span class="text-center w-full text-blue-600">{{ $step = add $step 1 }} {{ $step }}</span>
                    </div>
                    <span
                        class="font-bold ml-3 text-slate-700 dark:text-slate-400">{{ T "sidebar.tableEditor.title" }}</span>
                </div>
                <div class="text-slate-600 mt-4 text-sm">
                    {{ if eq .Params.Layout "converter" }}
                        {{ printf (T "sidebar.tableEditor.description.converter") (T (printf "converters.%s.alias" .Params.From)) }}
                    {{ else }}
                        {{ T "sidebar.tableEditor.description.generator" }}
                    {{ end }}
                </div>
            </div>
            <div class="">
                <div class="flex items-center">
                    <div class="w-7 h-7 border-2 border-blue-500 rounded-full flex items-center">
                        <span class="text-center w-full text-blue-600">{{ $step = add $step 1 }} {{ $step }}</span>
                    </div>
                    <span
                        class="font-bold ml-3 text-slate-700 dark:text-slate-400">{{ T "sidebar.tableGenerator.title" }}</span>
                </div>
                <div class="text-slate-600 mt-4 text-sm">
                    {{ if eq .Params.Layout "converter" }}
                        {{ printf (T "sidebar.tableGenerator.description.converter") (T (printf "converters.%s.alias" .Params.to)) }}
                    {{ else }}
                        {{ printf (T "sidebar.tableGenerator.description.generator") (T (printf "converters.%s.alias" .Params.to)) }}
                    {{ end }}
                </div>
            </div>
        </div>
    {{ end }}
    <div class="sponsors">
        <!--    <div class="w-full grid grid-cols-2 gap-3">-->
        <!--      <a target="_blank" title="Give $100 trial" href="https://www.vultr.com/?ref=8968058-8H" class="flex px-2 items-center justify-center h-14 hover:bg-slate-100 rounded-lg hover:transition-all border border-dashed border-slate-200 bg-slate-50 dark:bg-slate-800 dark:border-slate-800 dark:text-slate-400">-->
        <!--        <img src="https://www.vultr.com/media/logo_onwhite.svg?_gl=1*1fty8hn*_ga*MzkwOTkxNzczLjE2NzQ4MTkyODY.*_ga_K6536FHN4D*MTY3NTkxNDA0NS41LjEuMTY3NTkxNDU0Ny4wLjAuMA.." alt="Vultr">-->
        <!--      </a>-->
        <!--      <a target="_blank" title="Become a member" href="https://www.buymeacoffee.com/tableconvert/membership" class="flex px-2 items-center justify-center h-14 hover:bg-slate-100 rounded-lg hover:transition-all border border-dashed border-slate-200 bg-slate-50 dark:bg-slate-800 dark:border-slate-700 dark:text-slate-400">-->
        <!--        <p class="h-5 leading-5">❤️ Your logo</p>-->
        <!--      </a>-->
        <!--    </div>-->
        {{ partialCached "ad" . }}
    </div>
</div>
